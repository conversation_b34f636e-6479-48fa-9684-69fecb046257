# 知识图谱网站前端项目目录结构

```
knowledge-graph-frontend/
├── .vscode/                    # VSCode编辑器配置
├── public/                     # 静态资源文件夹
│   ├── favicon.ico             # 网站图标
│   ├── robots.txt              # 搜索引擎爬虫规则文件
│   └── static/                 # 其他静态资源
│       ├── images/             # 图片资源
│       └── icons/              # 图标资源
├── src/                        # 源代码目录
│   ├── api/                    # API请求模块
│   │   ├── axios.js            # Axios配置和请求拦截器
│   │   ├── auth.js             # 认证相关API
│   │   ├── graph.js            # 知识图谱相关API
│   │   ├── outline.js          # 大纲相关API
│   │   ├── share.js            # 分享相关API
│   │   └── user.js             # 用户相关API
│   ├── assets/                 # 资源文件
│   │   ├── styles/             # 样式文件
│   │   │   ├── global.css      # 全局样式
│   │   │   ├── theme.css       # 主题样式
│   │   │   └── variables.css   # CSS变量
│   │   ├── fonts/              # 字体文件
│   │   └── images/             # 图片资源
│   ├── components/             # 可复用组件
│   │   ├── common/             # 通用组件
│   │   │   ├── AppHeader.vue   # 应用头部组件
│   │   │   ├── AppFooter.vue   # 应用底部组件
│   │   │   ├── LoadingSpinner.vue # 加载动画组件
│   │   │   └── Notification.vue # 通知组件
│   │   ├── auth/               # 认证相关组件
│   │   │   ├── LoginForm.vue   # 登录表单
│   │   │   └── RegisterForm.vue # 注册表单
│   │   ├── graph/              # 图谱相关组件
│   │   │   ├── GraphCanvas.vue # 图谱画布组件
│   │   │   ├── NodeDetail.vue  # 节点详情组件
│   │   │   ├── GraphControls.vue # 图谱控制组件
│   │   │   └── LearningPath.vue # 学习路径组件
│   │   ├── outline/            # 大纲相关组件
│   │   │   ├── OutlineTree.vue # 大纲树组件
│   │   │   └── OutlineNode.vue # 大纲节点组件
│   │   └── share/              # 分享相关组件
│   │       ├── ShareCard.vue   # 分享卡片组件
│   │       ├── ShareForm.vue   # 分享表单组件
│   │       └── CommentList.vue # 评论列表组件
│   ├── composables/            # 组合式API函数
│   │   ├── useAuth.js          # 认证相关钩子
│   │   ├── useGraph.js         # 图谱操作钩子
│   │   └── useOutline.js       # 大纲操作钩子
│   ├── directives/             # 自定义指令
│   │   ├── clickOutside.js     # 点击外部指令
│   │   └── highlight.js        # 高亮指令
│   ├── layouts/                # 布局组件
│   │   ├── DefaultLayout.vue   # 默认布局
│   │   └── AuthLayout.vue      # 认证页面布局
│   ├── pages/                  # 页面组件
│   │   ├── HomePage.vue        # 首页
│   │   ├── OutlinePage.vue     # 大纲页面
│   │   ├── GraphPage.vue       # 知识图谱页面
│   │   ├── SharePage.vue       # 分享页面
│   │   ├── MyPage.vue          # 我的页面
│   │   ├── LoginPage.vue       # 登录页面
│   │   └── RegisterPage.vue    # 注册页面
│   ├── router/                 # 路由配置
│   │   ├── index.js            # 路由主文件
│   │   ├── routes.js           # 路由定义
│   │   └── guards.js           # 路由守卫
│   ├── stores/                 # Pinia状态管理
│   │   ├── auth.js             # 认证状态
│   │   ├── graph.js            # 图谱状态
│   │   ├── outline.js          # 大纲状态
│   │   ├── share.js            # 分享状态
│   │   └── user.js             # 用户状态
│   ├── utils/                  # 工具函数
│   │   ├── format.js           # 格式化函数
│   │   ├── validation.js       # 验证函数
│   │   ├── storage.js          # 本地存储工具
│   │   └── graph-utils.js      # 图谱处理工具
│   ├── views/                  # 视图组件（按角色分组）
│   │   ├── teacher/            # 教师视图
│   │   │   ├── GraphEditView.vue # 图谱编辑视图
│   │   │   └── MyGraphsView.vue # 我的图谱视图
│   │   └── student/            # 学生视图
│   │       ├── GraphLearningView.vue # 图谱学习视图
│   │       └── MyLearningView.vue # 我的学习视图
│   ├── App.vue                 # 应用根组件
│   └── main.js                 # 应用入口文件
├── .env                        # 环境变量
├── .env.development            # 开发环境变量
├── .env.production             # 生产环境变量
├── .eslintrc.js                # ESLint配置
├── .gitignore                  # Git忽略文件
├── index.html                  # HTML入口文件
├── package.json                # 项目依赖配置
├── README.md                   # 项目说明文档
├── vite.config.js              # Vite配置
└── tsconfig.json               # TypeScript配置(可选)
```

## 安全措施

1. **前端安全配置**
   - 配置Content Security Policy (CSP)以防止XSS攻击
   - 实现请求拦截器以处理JWT认证
   - 所有用户输入经过验证和转义
   - 敏感数据不存储在LocalStorage中
   - 针对SQL注入的前端防御（输入验证和过滤）

2. **模块说明**

   - **api目录**: 集中管理所有API请求，实现统一的错误处理和认证逻辑
   - **components**: 采用组件化开发，提高代码复用性和可维护性
   - **composables**: 使用Vue 3组合式API抽象业务逻辑
   - **stores**: 使用Pinia管理全局状态，支持模块化和TypeScript
   - **utils**: 通用工具函数，包含安全相关的输入处理和验证

3. **权限控制**
   - 基于用户角色的视图渲染控制
   - 路由守卫确保特定页面需要认证
   - 教师/学生角色页面分离，确保权限隔离

## 技术选择考虑

1. **Vue 3 + Vite**: 性能优异，支持组合式API，提供更好的组件化和代码组织方式
2. **Pinia**: 比Vuex更轻量，支持TypeScript，更符合Vue 3组合式API理念
3. **relation-graph-vue3**: 专为Vue 3设计的知识图谱可视化组件，支持复杂的节点关系展示
4. **基于文件的组件组织**: 清晰的目录结构使项目易于维护和扩展
5. **与MySQL集成**: 使用标准化API通信，确保与后端使用的MySQL数据库良好兼容

## 前端性能优化

1. **资源优化**
   - 使用Vite进行构建，实现快速的热模块替换
   - 组件懒加载减少初始加载时间
   - 图片资源优化和懒加载

2. **渲染优化**
   - 虚拟列表处理大数据集
   - 避免不必要的组件重渲染
   - 使用keep-alive缓存组件

3. **数据获取优化**
   - 实现数据缓存减少重复请求
   - 分页加载大型数据集
   - 数据预取提升用户体验 